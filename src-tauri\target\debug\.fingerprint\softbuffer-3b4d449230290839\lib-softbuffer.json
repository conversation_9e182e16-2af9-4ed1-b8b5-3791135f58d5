{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 10809724437792986082, "path": 1107112358080683955, "deps": [[376837177317575824, "build_script_build", false, 3094028869170983182], [4143744114649553716, "raw_window_handle", false, 12076753229495097825], [5986029879202738730, "log", false, 3050089541774172585], [10281541584571964250, "windows_sys", false, 17427696078853352968]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-3b4d449230290839\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}