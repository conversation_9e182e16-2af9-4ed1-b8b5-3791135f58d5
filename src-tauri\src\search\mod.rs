use serde::{Deserialize, Serialize};
use anyhow::Result;
use fuzzy_matcher::{Fuzzy<PERSON>atch<PERSON>, SkimMatcherV2};
use std::collections::HashMap;
use crate::browser::Tab;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SearchResult {
    pub id: String,
    pub title: String,
    pub content: String,
    pub url: Option<String>,
    pub score: f64,
    pub result_type: SearchResultType,
    pub highlights: Vec<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub enum SearchResultType {
    Tab,
    History,
    Bookmark,
    Memory,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchQuery {
    pub text: String,
    pub filters: Option<HashMap<String, String>>,
    pub limit: usize,
    pub include_types: Vec<SearchResultType>,
}

/// Lightweight search engine with fuzzy matching
#[derive(Debug)]
pub struct SearchEngine {
    matcher: SkimMatcherV2,
    history: Vec<SearchResult>,
    bookmarks: Vec<SearchResult>,
}

impl SearchEngine {
    pub async fn new() -> Result<Self> {
        let matcher = SkimMatcherV2::default();
        
        Ok(Self {
            matcher,
            history: Vec::new(),
            bookmarks: Vec::new(),
        })
    }

    /// Search across tabs with fuzzy matching
    pub async fn search_tabs(&self, query: &str, tabs: &[Tab]) -> Result<Vec<Tab>> {
        let mut scored_tabs: Vec<(Tab, i64)> = Vec::new();

        for tab in tabs {
            // Search in title
            let title_score = self.matcher.fuzzy_match(&tab.title, query).unwrap_or(0);
            
            // Search in URL
            let url_score = self.matcher.fuzzy_match(&tab.url, query).unwrap_or(0);
            
            // Combine scores with title weighted higher
            let total_score = (title_score * 2) + url_score;
            
            if total_score > 0 {
                scored_tabs.push((tab.clone(), total_score));
            }
        }

        // Sort by score descending
        scored_tabs.sort_by(|a, b| b.1.cmp(&a.1));
        
        // Return top results
        Ok(scored_tabs.into_iter().map(|(tab, _)| tab).take(10).collect())
    }

    /// Universal search across all content types
    pub async fn search(&self, query: SearchQuery) -> Result<Vec<SearchResult>> {
        let mut results = Vec::new();

        // Search tabs if included
        if query.include_types.contains(&SearchResultType::Tab) {
            // This would be called with actual tabs from browser engine
            // For now, return empty results
        }

        // Search history if included
        if query.include_types.contains(&SearchResultType::History) {
            let history_results = self.search_history(&query.text, query.limit / 4).await?;
            results.extend(history_results);
        }

        // Search bookmarks if included
        if query.include_types.contains(&SearchResultType::Bookmark) {
            let bookmark_results = self.search_bookmarks(&query.text, query.limit / 4).await?;
            results.extend(bookmark_results);
        }

        // Sort by score and limit results
        results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));
        results.truncate(query.limit);

        Ok(results)
    }

    /// Search browser history
    async fn search_history(&self, query: &str, limit: usize) -> Result<Vec<SearchResult>> {
        let mut results = Vec::new();

        for item in &self.history {
            let title_score = self.matcher.fuzzy_match(&item.title, query).unwrap_or(0) as f64;
            let content_score = self.matcher.fuzzy_match(&item.content, query).unwrap_or(0) as f64;
            
            let total_score = (title_score * 1.5) + content_score;
            
            if total_score > 0.0 {
                let mut result = item.clone();
                result.score = total_score / 100.0; // Normalize score
                result.highlights = self.extract_highlights(&item.content, query);
                results.push(result);
            }
        }

        results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));
        results.truncate(limit);

        Ok(results)
    }

    /// Search bookmarks
    async fn search_bookmarks(&self, query: &str, limit: usize) -> Result<Vec<SearchResult>> {
        let mut results = Vec::new();

        for item in &self.bookmarks {
            let title_score = self.matcher.fuzzy_match(&item.title, query).unwrap_or(0) as f64;
            let content_score = self.matcher.fuzzy_match(&item.content, query).unwrap_or(0) as f64;
            
            let total_score = (title_score * 2.0) + content_score; // Bookmarks weighted higher
            
            if total_score > 0.0 {
                let mut result = item.clone();
                result.score = total_score / 100.0;
                result.highlights = self.extract_highlights(&item.content, query);
                results.push(result);
            }
        }

        results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap_or(std::cmp::Ordering::Equal));
        results.truncate(limit);

        Ok(results)
    }

    /// Extract highlighted text snippets
    fn extract_highlights(&self, content: &str, query: &str) -> Vec<String> {
        let mut highlights = Vec::new();
        let query_lower = query.to_lowercase();
        let content_lower = content.to_lowercase();

        // Find matches and extract surrounding context
        if let Some(pos) = content_lower.find(&query_lower) {
            let start = pos.saturating_sub(30);
            let end = std::cmp::min(pos + query.len() + 30, content.len());
            
            if let Some(snippet) = content.get(start..end) {
                highlights.push(format!("...{snippet}..."));
            }
        }

        if highlights.is_empty() {
            // Fallback to first 60 characters
            let snippet = content.chars().take(60).collect::<String>();
            highlights.push(format!("{snippet}..."));
        }

        highlights
    }

    /// Add item to search history
    pub async fn add_to_history(&mut self, item: SearchResult) -> Result<()> {
        // Remove duplicates
        self.history.retain(|h| h.id != item.id);
        
        // Add to front
        self.history.insert(0, item);
        
        // Keep only recent items
        self.history.truncate(1000);
        
        Ok(())
    }

    /// Add bookmark
    pub async fn add_bookmark(&mut self, item: SearchResult) -> Result<()> {
        // Remove duplicates
        self.bookmarks.retain(|b| b.id != item.id);
        
        // Add bookmark
        self.bookmarks.push(item);
        
        Ok(())
    }

    /// Remove bookmark
    pub async fn remove_bookmark(&mut self, id: &str) -> Result<()> {
        self.bookmarks.retain(|b| b.id != id);
        Ok(())
    }

    /// Get search suggestions based on query
    pub async fn get_suggestions(&self, query: &str) -> Result<Vec<String>> {
        let mut suggestions = Vec::new();

        // Add common search patterns
        if query.len() >= 2 {
            // History-based suggestions
            for item in &self.history {
                if item.title.to_lowercase().contains(&query.to_lowercase()) {
                    suggestions.push(item.title.clone());
                }
                if suggestions.len() >= 5 {
                    break;
                }
            }

            // Bookmark-based suggestions
            for item in &self.bookmarks {
                if item.title.to_lowercase().contains(&query.to_lowercase()) && 
                   !suggestions.contains(&item.title) {
                    suggestions.push(item.title.clone());
                }
                if suggestions.len() >= 8 {
                    break;
                }
            }
        }

        Ok(suggestions)
    }

    /// Get search statistics
    pub async fn get_stats(&self) -> Result<serde_json::Value> {
        Ok(serde_json::json!({
            "history_items": self.history.len(),
            "bookmarks": self.bookmarks.len(),
            "total_searchable_items": self.history.len() + self.bookmarks.len(),
        }))
    }

    /// Clear search history
    pub async fn clear_history(&mut self) -> Result<()> {
        self.history.clear();
        Ok(())
    }

    /// Clear bookmarks
    pub async fn clear_bookmarks(&mut self) -> Result<()> {
        self.bookmarks.clear();
        Ok(())
    }
}
