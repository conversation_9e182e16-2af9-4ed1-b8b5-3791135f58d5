{"$schema": "../node_modules/@tauri-apps/cli/schema.json", "productName": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "identifier": "com.memori.browser", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"fullscreen": false, "resizable": true, "title": "<PERSON><PERSON><PERSON>", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": []}}