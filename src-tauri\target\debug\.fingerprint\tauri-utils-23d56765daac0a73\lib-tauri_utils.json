{"rustc": 1842507548689473721, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 2457740146775615380, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 18131997394239016100], [3060637413840920116, "proc_macro2", false, 14143828410907889523], [3150220818285335163, "url", false, 5650291493764149010], [3191507132440681679, "serde_untagged", false, 6620093224522947119], [4071963112282141418, "serde_with", false, 8142825095089252481], [4899080583175475170, "semver", false, 10885335960225221361], [5986029879202738730, "log", false, 1134941415235641757], [6606131838865521726, "ctor", false, 5074607611391229500], [6913375703034175521, "schemars", false, 9940543878405923545], [7170110829644101142, "json_patch", false, 7155364116303564817], [8319709847752024821, "uuid", false, 2416734466083964470], [9010263965687315507, "http", false, 11985562838604801511], [9451456094439810778, "regex", false, 2655336738413197755], [9556762810601084293, "brotli", false, 10165981582488774907], [9689903380558560274, "serde", false, 3158597207148056356], [10806645703491011684, "thiserror", false, 1294739587544922555], [11655476559277113544, "cargo_metadata", false, 6183853463533473691], [11989259058781683633, "dunce", false, 18370474174498096977], [13625485746686963219, "anyhow", false, 6404980772165172830], [14232843520438415263, "html5ever", false, 9016823318152823375], [15088007382495681292, "kuchiki", false, 9607627918024927363], [15367738274754116744, "serde_json", false, 16756031453236183760], [15609422047640926750, "toml", false, 16415255170703270083], [15622660310229662834, "walkdir", false, 17524825054824304106], [15932120279885307830, "memchr", false, 9973787961473383461], [17146114186171651583, "infer", false, 8362984614560454990], [17155886227862585100, "glob", false, 11939270752987892944], [17186037756130803222, "phf", false, 18313193305117651219], [17990358020177143287, "quote", false, 9791753232960825671]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-23d56765daac0a73\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}