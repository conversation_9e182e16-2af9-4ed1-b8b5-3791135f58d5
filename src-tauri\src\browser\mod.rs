use serde::{Deserialize, Serialize};
use anyhow::Result;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Tab {
    pub id: String,
    pub title: String,
    pub url: String,
    pub favicon: Option<String>,
    pub is_active: bool,
    pub is_loading: bool,
    pub created_at: DateTime<Utc>,
    pub last_accessed: DateTime<Utc>,
    pub visit_count: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NavigationHistory {
    pub entries: Vec<String>,
    pub current_index: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BrowserSession {
    pub id: String,
    pub name: String,
    pub tabs: Vec<Tab>,
    pub created_at: DateTime<Utc>,
    pub last_used: DateTime<Utc>,
}

/// Lightweight browser engine for tab and session management
#[derive(Debug)]
pub struct BrowserEngine {
    tabs: HashMap<String, Tab>,
    active_tab_id: Option<String>,
    sessions: HashMap<String, BrowserSession>,
    navigation_history: HashMap<String, NavigationHistory>,
}

impl BrowserEngine {
    pub async fn new() -> Result<Self> {
        let mut engine = Self {
            tabs: HashMap::new(),
            active_tab_id: None,
            sessions: HashMap::new(),
            navigation_history: HashMap::new(),
        };

        // Create initial tab
        let initial_tab = engine.create_tab("memori://newtab").await?;
        engine.switch_tab(&initial_tab.id).await?;

        Ok(engine)
    }

    /// Create a new tab
    pub async fn create_tab(&mut self, url: &str) -> Result<Tab> {
        let id = Uuid::new_v4().to_string();
        let now = Utc::now();

        let tab = Tab {
            id: id.clone(),
            title: self.get_title_from_url(url),
            url: url.to_string(),
            favicon: None,
            is_active: false,
            is_loading: false,
            created_at: now,
            last_accessed: now,
            visit_count: 1,
        };

        // Initialize navigation history
        self.navigation_history.insert(id.clone(), NavigationHistory {
            entries: vec![url.to_string()],
            current_index: 0,
        });

        self.tabs.insert(id.clone(), tab.clone());
        
        log::info!("Created new tab: {} - {}", id, url);
        Ok(tab)
    }

    /// Close a tab
    pub async fn close_tab(&mut self, tab_id: &str) -> Result<()> {
        if let Some(_tab) = self.tabs.remove(tab_id) {
            self.navigation_history.remove(tab_id);

            // If this was the active tab, switch to another
            if self.active_tab_id.as_ref() == Some(&tab_id.to_string()) {
                self.active_tab_id = None;
                
                // Switch to the first available tab
                if let Some(first_tab_id) = self.tabs.keys().next().cloned() {
                    self.switch_tab(&first_tab_id).await?;
                }
            }

            log::info!("Closed tab: {}", tab_id);
        }

        Ok(())
    }

    /// Switch to a specific tab
    pub async fn switch_tab(&mut self, tab_id: &str) -> Result<()> {
        if let Some(tab) = self.tabs.get_mut(tab_id) {
            // Deactivate current active tab
            if let Some(current_active_id) = &self.active_tab_id {
                if let Some(current_tab) = self.tabs.get_mut(current_active_id) {
                    current_tab.is_active = false;
                }
            }

            // Activate new tab
            tab.is_active = true;
            tab.last_accessed = Utc::now();
            tab.visit_count += 1;
            self.active_tab_id = Some(tab_id.to_string());

            log::info!("Switched to tab: {} - {}", tab_id, tab.title);
        }

        Ok(())
    }

    /// Navigate to URL in current tab
    pub async fn navigate(&mut self, url: &str) -> Result<()> {
        if let Some(active_id) = &self.active_tab_id.clone() {
            if let Some(tab) = self.tabs.get_mut(active_id) {
                tab.url = url.to_string();
                tab.title = self.get_title_from_url(url);
                tab.is_loading = true;
                tab.last_accessed = Utc::now();

                // Update navigation history
                if let Some(history) = self.navigation_history.get_mut(active_id) {
                    // Remove forward history if navigating to new URL
                    history.entries.truncate(history.current_index + 1);
                    history.entries.push(url.to_string());
                    history.current_index = history.entries.len() - 1;
                }

                log::info!("Navigating to: {}", url);
            }
        }

        Ok(())
    }

    /// Go back in navigation history
    pub async fn go_back(&mut self) -> Result<bool> {
        if let Some(active_id) = &self.active_tab_id.clone() {
            if let Some(history) = self.navigation_history.get_mut(active_id) {
                if history.current_index > 0 {
                    history.current_index -= 1;
                    let url = history.entries[history.current_index].clone();
                    
                    if let Some(tab) = self.tabs.get_mut(active_id) {
                        tab.url = url;
                        tab.title = self.get_title_from_url(&tab.url);
                        tab.last_accessed = Utc::now();
                    }

                    return Ok(true);
                }
            }
        }

        Ok(false)
    }

    /// Go forward in navigation history
    pub async fn go_forward(&mut self) -> Result<bool> {
        if let Some(active_id) = &self.active_tab_id.clone() {
            if let Some(history) = self.navigation_history.get_mut(active_id) {
                if history.current_index < history.entries.len() - 1 {
                    history.current_index += 1;
                    let url = history.entries[history.current_index].clone();
                    
                    if let Some(tab) = self.tabs.get_mut(active_id) {
                        tab.url = url;
                        tab.title = self.get_title_from_url(&tab.url);
                        tab.last_accessed = Utc::now();
                    }

                    return Ok(true);
                }
            }
        }

        Ok(false)
    }

    /// Refresh current tab
    pub async fn refresh(&mut self) -> Result<()> {
        if let Some(active_id) = &self.active_tab_id.clone() {
            if let Some(tab) = self.tabs.get_mut(active_id) {
                tab.is_loading = true;
                tab.last_accessed = Utc::now();
                log::info!("Refreshing tab: {}", tab.title);
            }
        }

        Ok(())
    }

    /// Get all tabs
    pub fn get_tabs(&self) -> Vec<Tab> {
        let mut tabs: Vec<Tab> = self.tabs.values().cloned().collect();
        tabs.sort_by(|a, b| b.last_accessed.cmp(&a.last_accessed));
        tabs
    }

    /// Get active tab
    pub fn get_active_tab(&self) -> Option<&Tab> {
        self.active_tab_id.as_ref().and_then(|id| self.tabs.get(id))
    }

    /// Save current session
    pub async fn save_session(&mut self, name: &str) -> Result<String> {
        let id = Uuid::new_v4().to_string();
        let now = Utc::now();

        let session = BrowserSession {
            id: id.clone(),
            name: name.to_string(),
            tabs: self.get_tabs(),
            created_at: now,
            last_used: now,
        };

        self.sessions.insert(id.clone(), session);
        log::info!("Saved session: {} - {}", id, name);

        Ok(id)
    }

    /// Restore session
    pub async fn restore_session(&mut self, session_id: &str) -> Result<()> {
        if let Some(session) = self.sessions.get(session_id).cloned() {
            // Clear current tabs
            self.tabs.clear();
            self.navigation_history.clear();
            self.active_tab_id = None;

            // Restore tabs from session
            for tab in session.tabs {
                self.tabs.insert(tab.id.clone(), tab.clone());
                
                // Initialize navigation history
                self.navigation_history.insert(tab.id.clone(), NavigationHistory {
                    entries: vec![tab.url.clone()],
                    current_index: 0,
                });

                if tab.is_active {
                    self.active_tab_id = Some(tab.id);
                }
            }

            log::info!("Restored session: {}", session.name);
        }

        Ok(())
    }

    /// Get all sessions
    pub fn get_sessions(&self) -> Vec<&BrowserSession> {
        let mut sessions: Vec<&BrowserSession> = self.sessions.values().collect();
        sessions.sort_by(|a, b| b.last_used.cmp(&a.last_used));
        sessions
    }

    /// Delete session
    pub async fn delete_session(&mut self, session_id: &str) -> Result<()> {
        self.sessions.remove(session_id);
        Ok(())
    }

    /// Get browser statistics
    pub async fn get_stats(&self) -> Result<serde_json::Value> {
        let total_tabs = self.tabs.len();
        let total_sessions = self.sessions.len();
        let active_tab = self.get_active_tab();

        Ok(serde_json::json!({
            "total_tabs": total_tabs,
            "total_sessions": total_sessions,
            "active_tab": active_tab.map(|t| &t.id),
            "memory_usage_estimate": total_tabs * 10, // Rough estimate in MB
        }))
    }

    /// Helper function to extract title from URL
    fn get_title_from_url(&self, url: &str) -> String {
        if url == "memori://newtab" {
            return "New Tab".to_string();
        }

        if let Ok(parsed_url) = url::Url::parse(url) {
            if let Some(domain) = parsed_url.domain() {
                return domain.to_string();
            }
        }

        url.to_string()
    }

    /// Update tab title (called when page loads)
    pub async fn update_tab_title(&mut self, tab_id: &str, title: &str) -> Result<()> {
        if let Some(tab) = self.tabs.get_mut(tab_id) {
            tab.title = title.to_string();
            tab.is_loading = false;
        }
        Ok(())
    }

    /// Update tab favicon
    pub async fn update_tab_favicon(&mut self, tab_id: &str, favicon_url: &str) -> Result<()> {
        if let Some(tab) = self.tabs.get_mut(tab_id) {
            tab.favicon = Some(favicon_url.to_string());
        }
        Ok(())
    }
}
