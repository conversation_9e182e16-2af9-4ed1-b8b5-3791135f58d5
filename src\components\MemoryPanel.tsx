import React, { useState, useEffect } from "react";
import { Brain, Clock, Tag, Search, Plus } from "lucide-react";
import { useBrowserStore } from "../stores/browserStore";

export function MemoryPanel() {
  const [localSearchQuery, setLocalSearchQuery] = useState("");
  const { memoryItems, searchMemory, saveMemory, searchQuery } = useBrowserStore();

  useEffect(() => {
    // Load initial memory items
    searchMemory("");
  }, [searchMemory]);

  const handleSearch = async (query: string) => {
    setLocalSearchQuery(query);
    await searchMemory(query);
  };

  const handleSaveCurrentPage = async () => {
    // Mock saving current page to memory
    const mockContent = "Current page content would be extracted here";
    const mockMetadata = {
      url: "https://example.com",
      title: "Example Page",
      type: "webpage",
      tags: ["example", "demo"]
    };
    
    await saveMemory(mockContent, mockMetadata);
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) return "Just now";
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  return (
    <div className="h-full flex flex-col bg-gray-800">
      {/* Header */}
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center gap-2 mb-3">
          <Brain className="w-5 h-5 text-memori-500" />
          <h2 className="text-lg font-semibold text-white">Memory</h2>
          <button
            onClick={handleSaveCurrentPage}
            className="ml-auto p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
            title="Save current page"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>
        
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            value={localSearchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            placeholder="Search memories..."
            className="w-full pl-10 pr-4 py-2 bg-gray-900 border border-gray-600 rounded text-white placeholder-gray-400 text-sm focus:outline-none focus:ring-2 focus:ring-memori-500"
          />
        </div>
      </div>

      {/* Memory Items */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {memoryItems.length === 0 ? (
          <div className="text-center text-gray-400 mt-8">
            <Brain className="w-12 h-12 mx-auto mb-4 opacity-50" />
            <p className="text-sm">No memories found</p>
            <p className="text-xs mt-1">
              {localSearchQuery ? "Try a different search" : "Start browsing to build memories"}
            </p>
          </div>
        ) : (
          memoryItems.map((item) => (
            <div
              key={item.id}
              className="p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition-colors group"
            >
              <div className="text-sm text-white mb-2 line-clamp-3">
                {item.content.length > 150 
                  ? `${item.content.substring(0, 150)}...` 
                  : item.content
                }
              </div>
              
              <div className="flex items-center gap-2 text-xs text-gray-400 mb-2">
                <Clock className="w-3 h-3" />
                <span>{formatTimeAgo(item.last_accessed)}</span>
                <span className="ml-auto">
                  {item.relevance_score 
                    ? `${Math.round(item.relevance_score * 100)}% match`
                    : `${item.access_count} views`
                  }
                </span>
              </div>
              
              {item.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {item.tags.slice(0, 3).map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center gap-1 px-2 py-1 bg-gray-600 text-gray-300 rounded text-xs"
                    >
                      <Tag className="w-2 h-2" />
                      {tag}
                    </span>
                  ))}
                  {item.tags.length > 3 && (
                    <span className="text-xs text-gray-400">
                      +{item.tags.length - 3} more
                    </span>
                  )}
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Footer */}
      <div className="p-4 border-t border-gray-700">
        <div className="text-xs text-gray-400 text-center">
          {memoryItems.length} memories • AI-enhanced
        </div>
        {searchQuery && (
          <div className="text-xs text-gray-500 text-center mt-1">
            Searching: "{searchQuery}"
          </div>
        )}
      </div>
    </div>
  );
}
