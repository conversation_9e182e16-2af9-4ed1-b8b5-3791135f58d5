[package]
name = "memori-browser"
version = "0.1.0"
description = "The lightest, fastest, AI-enhanced browser"
authors = ["Memori Team"]
license = "MIT"
repository = "https://github.com/your-username/memori-browser"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "1.5", features = [] }

[dependencies]
tauri = { version = "1.5", features = [
  "api-all",
  "devtools",
  "shell-open",
  "window-all",
  "fs-all",
  "dialog-all",
  "notification-all",
  "global-shortcut-all",
  "system-tray",
  "updater"
] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
thiserror = "1.0"
log = "0.4"
env_logger = "0.10"
reqwest = { version = "0.11", features = ["json", "rustls-tls"] }
url = "2.4"
regex = "1.10"
fuzzy-matcher = "0.3"
tantivy = "0.21"  # For full-text search
dirs = "5.0"

# AI/ML dependencies
candle-core = "0.3"
candle-nn = "0.3"
candle-transformers = "0.3"
hf-hub = "0.3"
tokenizers = "0.15"

# Vector database
qdrant-client = "1.7"

[features]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]

# Performance optimizations
[profile.release]
panic = "abort"
codegen-units = 1
lto = true
opt-level = "s"  # Optimize for size
strip = true     # Remove debug symbols

[profile.dev]
opt-level = 1    # Some optimization for faster dev builds
