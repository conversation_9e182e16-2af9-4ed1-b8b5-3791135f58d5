{"name": "memori-browser", "version": "0.1.0", "description": "The lightest, fastest, AI-enhanced browser across all platforms", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "tauri:build:debug": "tauri build --debug", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,md}", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@tauri-apps/api": "^1.5.3", "@tauri-apps/plugin-shell": "^1.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "zustand": "^4.4.7", "flexsearch": "^0.7.31", "fuse.js": "^7.0.0", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@tauri-apps/cli": "^1.5.8", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8", "vitest": "^1.0.4"}, "keywords": ["browser", "ai", "tauri", "rust", "react", "lightweight", "fast", "memory", "search", "cross-platform"], "author": "Memori Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/memori-browser"}, "bugs": {"url": "https://github.com/your-username/memori-browser/issues"}, "homepage": "https://memori-browser.dev"}