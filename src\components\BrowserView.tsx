import React from "react";
import { ArrowLeft, ArrowRight, RotateCcw, Home } from "lucide-react";
import { useBrowserStore } from "../stores/browserStore";

export function BrowserView() {
  const { tabs, activeTabId } = useBrowserStore();
  const activeTab = tabs.find(tab => tab.id === activeTabId);
  const currentUrl = activeTab?.url || "memori://newtab";

  const handleBack = () => {
    // TODO: Implement navigation
    console.log("Going back");
  };

  const handleForward = () => {
    // TODO: Implement navigation
    console.log("Going forward");
  };

  const handleRefresh = () => {
    // TODO: Implement refresh
    console.log("Refreshing");
  };

  const handleHome = () => {
    // TODO: Implement home navigation
    console.log("Going home");
  };

  const handleUrlChange = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const url = e.currentTarget.value;
      // TODO: Navigate to URL
      console.log("Navigating to:", url);
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Navigation Bar */}
      <div className="flex items-center gap-2 p-2 bg-gray-800 border-b border-gray-700">
        <button
          onClick={handleBack}
          className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
        </button>
        <button
          onClick={handleForward}
          className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
        >
          <ArrowRight className="w-4 h-4" />
        </button>
        <button
          onClick={handleRefresh}
          className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
        >
          <RotateCcw className="w-4 h-4" />
        </button>
        <button
          onClick={handleHome}
          className="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors"
        >
          <Home className="w-4 h-4" />
        </button>
        
        {/* URL Bar */}
        <div className="flex-1 mx-4">
          <input
            type="text"
            defaultValue={currentUrl}
            onKeyDown={handleUrlChange}
            className="w-full px-3 py-1 bg-gray-900 border border-gray-600 rounded text-white text-sm focus:outline-none focus:ring-2 focus:ring-memori-500"
            placeholder="Enter URL or search..."
          />
        </div>
      </div>

      {/* Webview Container */}
      <div className="flex-1 bg-white">
        {currentUrl === "memori://newtab" ? (
          <div className="h-full flex items-center justify-center bg-gradient-to-br from-gray-900 to-gray-800">
            <div className="text-center text-white">
              <div className="text-8xl mb-6">🧠</div>
              <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                Welcome to Memori
              </h1>
              <p className="text-xl text-gray-300 mb-8">
                The AI-enhanced browser that remembers everything
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
                  <div className="text-3xl mb-3">⚡</div>
                  <h3 className="text-lg font-semibold mb-2">Lightning Fast</h3>
                  <p className="text-gray-400">Built with Rust for maximum performance</p>
                </div>
                <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
                  <div className="text-3xl mb-3">🔍</div>
                  <h3 className="text-lg font-semibold mb-2">Smart Search</h3>
                  <p className="text-gray-400">AI-powered search across all your browsing</p>
                </div>
                <div className="bg-gray-800 p-6 rounded-lg border border-gray-700">
                  <div className="text-3xl mb-3">🛡️</div>
                  <h3 className="text-lg font-semibold mb-2">Privacy First</h3>
                  <p className="text-gray-400">Your data stays on your device</p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center bg-gray-100">
            <div className="text-center text-gray-600">
              <div className="text-4xl mb-4">🌐</div>
              <h2 className="text-xl font-semibold mb-2">Loading...</h2>
              <p className="text-gray-500">{currentUrl}</p>
              <p className="text-sm text-gray-400 mt-4">
                Webview integration coming soon
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
