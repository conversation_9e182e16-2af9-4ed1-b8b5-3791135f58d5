import React, { useState } from "react";
import { <PERSON>, Mic, Settings } from "lucide-react";
import { useBrowserStore } from "../stores/browserStore";

export function SearchBar() {
  const [query, setQuery] = useState("");
  const [isListening, setIsListening] = useState(false);
  const { searchMemory, setSearchQuery, getAISuggestion } = useBrowserStore();

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim()) return;

    // Set search query in store
    setSearchQuery(query);
    
    // Search memory
    await searchMemory(query);
    
    // Get AI suggestion for the search
    try {
      const suggestion = await getAISuggestion(`User searched for: ${query}`);
      console.log("AI Suggestion:", suggestion);
    } catch (error) {
      console.error("Failed to get AI suggestion:", error);
    }
  };

  const toggleVoiceSearch = () => {
    setIsListening(!isListening);
    // TODO: Implement voice search with Web Speech API
    if (!isListening) {
      console.log("Starting voice search...");
    } else {
      console.log("Stopping voice search...");
    }
  };

  return (
    <div className="flex items-center gap-4 p-4 bg-gray-800 border-b border-gray-700">
      <form onSubmit={handleSearch} className="flex-1 flex items-center gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Search tabs, history, or ask AI..."
            className="w-full pl-10 pr-4 py-2 bg-gray-900 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-memori-500"
          />
        </div>
        <button
          type="button"
          onClick={toggleVoiceSearch}
          className={`p-2 rounded-lg transition-colors ${
            isListening 
              ? "bg-red-500 text-white" 
              : "bg-gray-700 text-gray-300 hover:bg-gray-600"
          }`}
        >
          <Mic className="w-4 h-4" />
        </button>
      </form>
      <button className="p-2 bg-gray-700 text-gray-300 hover:bg-gray-600 rounded-lg transition-colors">
        <Settings className="w-4 h-4" />
      </button>
    </div>
  );
}
