{"rustc": 1842507548689473721, "features": "[\"devtools\"]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 10809724437792986082, "path": 4082272819559425104, "deps": [[2671782512663819132, "tauri_utils", false, 16766761922843275745], [3150220818285335163, "url", false, 3125953265746058012], [4143744114649553716, "raw_window_handle", false, 12076753229495097825], [6089812615193535349, "build_script_build", false, 5524708087779544063], [7606335748176206944, "dpi", false, 424038657922711694], [9010263965687315507, "http", false, 11529510996351137805], [9689903380558560274, "serde", false, 6629062398223269867], [10806645703491011684, "thiserror", false, 6290649561672044865], [14585479307175734061, "windows", false, 14774998010591814940], [15367738274754116744, "serde_json", false, 7297581080823534964], [16727543399706004146, "cookie", false, 2239833576379844280]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-b5e2f55917d2cdd2\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}