{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463************, "path": 13131536037850374534, "deps": [[2671782512663819132, "tauri_utils", false, 13635082913196281338], [3060637413840920116, "proc_macro2", false, 14143828410907889523], [3150220818285335163, "url", false, 5650291493764149010], [4899080583175475170, "semver", false, 10885335960225221361], [4974441333307933176, "syn", false, 4149661804382479018], [7170110829644101142, "json_patch", false, 7155364116303564817], [7392050791754369441, "ico", false, 7629597027086246491], [8319709847752024821, "uuid", false, 2416734466083964470], [9556762810601084293, "brotli", false, 10165981582488774907], [9689903380558560274, "serde", false, 3158597207148056356], [9857275760291862238, "sha2", false, 7000312947546632887], [10806645703491011684, "thiserror", false, 1294739587544922555], [12687914511023397207, "png", false, 15722801764774915128], [13077212702700853852, "base64", false, 15659412035968304701], [15367738274754116744, "serde_json", false, 16756031453236183760], [15622660310229662834, "walkdir", false, 17524825054824304106], [17990358020177143287, "quote", false, 9791753232960825671]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-24ec39fa120a7df9\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}