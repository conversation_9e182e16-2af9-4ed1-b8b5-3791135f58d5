{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 5385403455254516259, "deps": [[2671782512663819132, "tauri_utils", false, 13635082913196281338], [3060637413840920116, "proc_macro2", false, 14143828410907889523], [4974441333307933176, "syn", false, 4149661804382479018], [13077543566650298139, "heck", false, 11003689655865491247], [14455244907590647360, "tauri_codegen", false, 8108994108660904043], [17990358020177143287, "quote", false, 9791753232960825671]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-e5102d4e95538da3\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}