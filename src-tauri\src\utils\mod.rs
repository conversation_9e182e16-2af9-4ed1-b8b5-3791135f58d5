use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::time::{SystemTime, UNIX_EPOCH};

/// Performance monitoring utilities
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub startup_time_ms: u64,
    pub memory_usage_mb: f64,
    pub tab_switch_time_ms: u64,
    pub search_time_ms: u64,
    pub ai_response_time_ms: u64,
}

/// Application configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub theme: String,
    pub default_search_engine: String,
    pub enable_ai: bool,
    pub enable_memory: bool,
    pub max_tabs: usize,
    pub auto_save_sessions: bool,
    pub privacy_mode: bool,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            theme: "dark".to_string(),
            default_search_engine: "https://www.google.com/search?q=".to_string(),
            enable_ai: true,
            enable_memory: true,
            max_tabs: 50,
            auto_save_sessions: true,
            privacy_mode: false,
        }
    }
}

/// Utility functions for the application
pub struct Utils;

impl Utils {
    /// Get current timestamp in milliseconds
    pub fn current_timestamp_ms() -> u64 {
        SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_millis() as u64
    }

    /// Format file size in human readable format
    pub fn format_file_size(bytes: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        let mut size = bytes as f64;
        let mut unit_index = 0;

        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }

        if unit_index == 0 {
            format!("{} {}", bytes, UNITS[unit_index])
        } else {
            format!("{:.1} {}", size, UNITS[unit_index])
        }
    }

    /// Sanitize URL for safe usage
    pub fn sanitize_url(url: &str) -> String {
        let url = url.trim();
        
        // Handle special Memori URLs
        if url.starts_with("memori://") {
            return url.to_string();
        }

        // Add protocol if missing
        if !url.starts_with("http://") && !url.starts_with("https://") {
            if url.contains('.') && !url.contains(' ') {
                return format!("https://{}", url);
            } else {
                // Treat as search query
                return format!("https://www.google.com/search?q={}", urlencoding::encode(url));
            }
        }

        url.to_string()
    }

    /// Extract domain from URL
    pub fn extract_domain(url: &str) -> Option<String> {
        if let Ok(parsed_url) = url::Url::parse(url) {
            parsed_url.domain().map(|d| d.to_string())
        } else {
            None
        }
    }

    /// Check if URL is valid
    pub fn is_valid_url(url: &str) -> bool {
        url::Url::parse(url).is_ok()
    }

    /// Generate a short ID
    pub fn generate_short_id() -> String {
        use uuid::Uuid;
        let uuid = Uuid::new_v4();
        uuid.to_string().split('-').next().unwrap_or("unknown").to_string()
    }

    /// Truncate text to specified length
    pub fn truncate_text(text: &str, max_length: usize) -> String {
        if text.len() <= max_length {
            text.to_string()
        } else {
            format!("{}...", &text[..max_length.saturating_sub(3)])
        }
    }

    /// Calculate similarity between two strings (simple implementation)
    pub fn calculate_similarity(a: &str, b: &str) -> f64 {
        let a_lower = a.to_lowercase();
        let b_lower = b.to_lowercase();

        if a_lower == b_lower {
            return 1.0;
        }

        // Simple Jaccard similarity
        let a_chars: std::collections::HashSet<char> = a_lower.chars().collect();
        let b_chars: std::collections::HashSet<char> = b_lower.chars().collect();

        let intersection = a_chars.intersection(&b_chars).count();
        let union = a_chars.union(&b_chars).count();

        if union == 0 {
            0.0
        } else {
            intersection as f64 / union as f64
        }
    }

    /// Load application configuration
    pub async fn load_config() -> Result<AppConfig> {
        let config_path = dirs::config_dir()
            .ok_or_else(|| anyhow::anyhow!("Could not find config directory"))?
            .join("memori")
            .join("config.json");

        if config_path.exists() {
            let content = tokio::fs::read_to_string(&config_path).await?;
            let config: AppConfig = serde_json::from_str(&content)?;
            Ok(config)
        } else {
            // Create default config
            let config = AppConfig::default();
            Self::save_config(&config).await?;
            Ok(config)
        }
    }

    /// Save application configuration
    pub async fn save_config(config: &AppConfig) -> Result<()> {
        let config_dir = dirs::config_dir()
            .ok_or_else(|| anyhow::anyhow!("Could not find config directory"))?
            .join("memori");

        tokio::fs::create_dir_all(&config_dir).await?;

        let config_path = config_dir.join("config.json");
        let content = serde_json::to_string_pretty(config)?;
        tokio::fs::write(&config_path, content).await?;

        Ok(())
    }

    /// Get system information
    pub fn get_system_info() -> serde_json::Value {
        serde_json::json!({
            "platform": std::env::consts::OS,
            "arch": std::env::consts::ARCH,
            "version": env!("CARGO_PKG_VERSION"),
            "build_time": env!("VERGEN_BUILD_TIMESTAMP"),
        })
    }

    /// Measure execution time of a function
    pub async fn measure_time<F, Fut, T>(f: F) -> (T, u64)
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = T>,
    {
        let start = std::time::Instant::now();
        let result = f().await;
        let duration = start.elapsed().as_millis() as u64;
        (result, duration)
    }

    /// Clean up old files and data
    pub async fn cleanup_old_data() -> Result<()> {
        let data_dir = dirs::data_dir()
            .ok_or_else(|| anyhow::anyhow!("Could not find data directory"))?
            .join("memori");

        if !data_dir.exists() {
            return Ok(());
        }

        // Clean up old log files (keep last 7 days)
        let logs_dir = data_dir.join("logs");
        if logs_dir.exists() {
            let cutoff = SystemTime::now() - std::time::Duration::from_secs(7 * 24 * 60 * 60);
            
            let mut entries = tokio::fs::read_dir(&logs_dir).await?;
            while let Some(entry) = entries.next_entry().await? {
                if let Ok(metadata) = entry.metadata().await {
                    if let Ok(modified) = metadata.modified() {
                        if modified < cutoff {
                            let _ = tokio::fs::remove_file(entry.path()).await;
                        }
                    }
                }
            }
        }

        Ok(())
    }

    /// Validate and normalize search query
    pub fn normalize_search_query(query: &str) -> String {
        query
            .trim()
            .to_lowercase()
            .chars()
            .filter(|c| c.is_alphanumeric() || c.is_whitespace() || *c == '-' || *c == '_')
            .collect::<String>()
            .split_whitespace()
            .collect::<Vec<&str>>()
            .join(" ")
    }

    /// Check if running in development mode
    pub fn is_development() -> bool {
        cfg!(debug_assertions)
    }

    /// Get application data directory
    pub fn get_app_data_dir() -> Result<std::path::PathBuf> {
        let data_dir = dirs::data_dir()
            .ok_or_else(|| anyhow::anyhow!("Could not find data directory"))?
            .join("memori");

        std::fs::create_dir_all(&data_dir)?;
        Ok(data_dir)
    }

    /// Get application cache directory
    pub fn get_app_cache_dir() -> Result<std::path::PathBuf> {
        let cache_dir = dirs::cache_dir()
            .ok_or_else(|| anyhow::anyhow!("Could not find cache directory"))?
            .join("memori");

        std::fs::create_dir_all(&cache_dir)?;
        Ok(cache_dir)
    }
}
