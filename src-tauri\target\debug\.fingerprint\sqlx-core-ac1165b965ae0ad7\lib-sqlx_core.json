{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 10809724437792986082, "path": 14993654793266126027, "deps": [[5103565458935487, "futures_io", false, 9051726668380001100], [40386456601120721, "percent_encoding", false, 17602416279374786006], [530211389790465181, "hex", false, 8571920594771044668], [788558663644978524, "crossbeam_queue", false, 12338071866724807146], [966925859616469517, "ahash", false, 5157532831126333462], [1162433738665300155, "crc", false, 1779429613318896735], [1464803193346256239, "event_listener", false, 2945712749853324523], [1811549171721445101, "futures_channel", false, 12376004183716733824], [3150220818285335163, "url", false, 3125953265746058012], [3405817021026194662, "hashlink", false, 17765826033932697459], [3646857438214563691, "futures_intrusive", false, 8725010590508785565], [3666196340704888985, "smallvec", false, 15449094074867167747], [3712811570531045576, "byteorder", false, 1255317013277435300], [3722963349756955755, "once_cell", false, 1443146423828032470], [5986029879202738730, "log", false, 3050089541774172585], [6493259146304816786, "indexmap", false, 10459542634981695654], [7620660491849607393, "futures_core", false, 15648460492167422919], [8008191657135824715, "thiserror", false, 3950923314837199705], [8319709847752024821, "uuid", false, 6349249694372189590], [8606274917505247608, "tracing", false, 11782835704813986857], [9689903380558560274, "serde", false, 6629062398223269867], [9857275760291862238, "sha2", false, 13022198141218923973], [9897246384292347999, "chrono", false, 7420787129642568160], [10629569228670356391, "futures_util", false, 2130271602361178218], [10862088793507253106, "sqlformat", false, 12307413540349487700], [11295624341523567602, "rustls", false, 3284958607360263734], [12170264697963848012, "either", false, 13423081270817475744], [12393800526703971956, "tokio", false, 4564033278980346352], [15367738274754116744, "serde_json", false, 7297581080823534964], [15932120279885307830, "memchr", false, 1932448928200586278], [16066129441945555748, "bytes", false, 15851513111714293082], [16311359161338405624, "rustls_pemfile", false, 6820821265403460970], [16973251432615581304, "tokio_stream", false, 12626015987126501127], [17106256174509013259, "atoi", false, 3826438781791101334], [17605717126308396068, "paste", false, 3230105283163785482], [17652733826348741533, "webpki_roots", false, 17307823372150873358]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-core-ac1165b965ae0ad7\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}