<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Me<PERSON><PERSON>er - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        memori: {
                            50: '#f0f9ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            900: '#1e293b',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .line-clamp-3 {
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</head>
<body class="bg-gray-900 text-white h-screen overflow-hidden">
    <div class="h-screen flex flex-col">
        <!-- Search Bar -->
        <div class="flex items-center gap-4 p-4 bg-gray-800 border-b border-gray-700">
            <div class="flex-1 flex items-center gap-2">
                <div class="relative flex-1">
                    <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <input
                        type="text"
                        id="searchInput"
                        placeholder="Search tabs, history, or ask AI..."
                        class="w-full pl-10 pr-4 py-2 bg-gray-900 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-memori-500"
                        onkeyup="handleSearch(event)"
                    />
                </div>
                <button onclick="toggleVoiceSearch()" id="voiceBtn" class="p-2 bg-gray-700 text-gray-300 hover:bg-gray-600 rounded-lg transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                    </svg>
                </button>
            </div>
            <button class="p-2 bg-gray-700 text-gray-300 hover:bg-gray-600 rounded-lg transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                </button>
        </div>

        <!-- Tab Bar -->
        <div class="flex items-center bg-gray-800 border-b border-gray-700 overflow-x-auto">
            <div id="tabContainer" class="flex">
                <!-- Tabs will be inserted here -->
            </div>
            <button onclick="createNewTab()" class="flex items-center justify-center w-10 h-10 text-gray-400 hover:text-white hover:bg-gray-700 transition-colors">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
            </button>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex min-h-0">
            <!-- Browser View -->
            <div class="flex-1 min-w-0 flex flex-col">
                <!-- Navigation Bar -->
                <div class="flex items-center gap-2 p-2 bg-gray-800 border-b border-gray-700">
                    <button onclick="goBack()" class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <button onclick="goForward()" class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                    <button onclick="refresh()" class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                        </svg>
                    </button>
                    <button onclick="goHome()" class="p-2 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                    </button>
                    <div class="flex-1 mx-4">
                        <input
                            type="text"
                            id="urlBar"
                            value="memori://newtab"
                            class="w-full px-3 py-1 bg-gray-900 border border-gray-600 rounded text-white text-sm focus:outline-none focus:ring-2 focus:ring-memori-500"
                            onkeyup="handleUrlInput(event)"
                        />
                    </div>
                </div>

                <!-- Content Area -->
                <div id="contentArea" class="flex-1 bg-gradient-to-br from-gray-900 to-gray-800">
                    <!-- New Tab Page -->
                    <div class="h-full flex items-center justify-center">
                        <div class="text-center text-white">
                            <div class="text-8xl mb-6">🧠</div>
                            <h1 class="text-4xl font-bold mb-4 bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                                Welcome to Memori
                            </h1>
                            <p class="text-xl text-gray-300 mb-8">
                                The AI-enhanced browser that remembers everything
                            </p>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
                                <div class="bg-gray-800 p-6 rounded-lg border border-gray-700 hover:border-memori-500 transition-colors cursor-pointer">
                                    <div class="text-3xl mb-3">⚡</div>
                                    <h3 class="text-lg font-semibold mb-2">Lightning Fast</h3>
                                    <p class="text-gray-400">Built with Rust for maximum performance</p>
                                </div>
                                <div class="bg-gray-800 p-6 rounded-lg border border-gray-700 hover:border-memori-500 transition-colors cursor-pointer">
                                    <div class="text-3xl mb-3">🔍</div>
                                    <h3 class="text-lg font-semibold mb-2">Smart Search</h3>
                                    <p class="text-gray-400">AI-powered search across all your browsing</p>
                                </div>
                                <div class="bg-gray-800 p-6 rounded-lg border border-gray-700 hover:border-memori-500 transition-colors cursor-pointer">
                                    <div class="text-3xl mb-3">🛡️</div>
                                    <h3 class="text-lg font-semibold mb-2">Privacy First</h3>
                                    <p class="text-gray-400">Your data stays on your device</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Memory Panel -->
            <div class="w-80 border-l border-gray-700 flex-shrink-0 bg-gray-800 flex flex-col">
                <!-- Header -->
                <div class="p-4 border-b border-gray-700">
                    <div class="flex items-center gap-2 mb-3">
                        <svg class="w-5 h-5 text-memori-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        <h2 class="text-lg font-semibold text-white">Memory</h2>
                        <button onclick="saveCurrentPage()" class="ml-auto p-1 text-gray-400 hover:text-white hover:bg-gray-700 rounded transition-colors" title="Save current page">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                        </button>
                    </div>
                    
                    <!-- Memory Search -->
                    <div class="relative">
                        <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <input
                            type="text"
                            id="memorySearch"
                            placeholder="Search memories..."
                            class="w-full pl-10 pr-4 py-2 bg-gray-900 border border-gray-600 rounded text-white placeholder-gray-400 text-sm focus:outline-none focus:ring-2 focus:ring-memori-500"
                            onkeyup="searchMemory(event)"
                        />
                    </div>
                </div>

                <!-- Memory Items -->
                <div id="memoryItems" class="flex-1 overflow-y-auto p-4 space-y-3">
                    <!-- Memory items will be inserted here -->
                </div>

                <!-- Footer -->
                <div class="p-4 border-t border-gray-700">
                    <div class="text-xs text-gray-400 text-center">
                        <span id="memoryCount">0</span> memories • AI-enhanced
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Demo data and functionality
        let tabs = [
            { id: 1, title: "New Tab", url: "memori://newtab", isActive: true }
        ];
        
        let memories = [
            {
                id: 1,
                content: "React performance optimization techniques for large applications",
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
                tags: ["react", "performance", "optimization"],
                relevance: 0.95
            },
            {
                id: 2,
                content: "Tauri vs Electron: A comprehensive comparison of desktop app frameworks",
                timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
                tags: ["tauri", "electron", "desktop", "comparison"],
                relevance: 0.87
            },
            {
                id: 3,
                content: "Building AI-enhanced applications with local LLMs and vector databases",
                timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
                tags: ["ai", "llm", "vector-db", "local"],
                relevance: 0.92
            }
        ];

        let activeTabId = 1;
        let isVoiceListening = false;

        function renderTabs() {
            const container = document.getElementById('tabContainer');
            container.innerHTML = tabs.map(tab => `
                <div class="flex items-center gap-2 px-4 py-2 min-w-0 max-w-xs cursor-pointer group ${
                    tab.isActive ? 'bg-gray-700 border-b-2 border-memori-500' : 'hover:bg-gray-700'
                }" onclick="switchTab(${tab.id})">
                    <div class="flex items-center gap-2 min-w-0 flex-1">
                        <svg class="w-4 h-4 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"></path>
                        </svg>
                        <span class="text-sm text-white truncate">${tab.title}</span>
                    </div>
                    <button onclick="closeTab(event, ${tab.id})" class="opacity-0 group-hover:opacity-100 p-1 hover:bg-gray-600 rounded transition-all">
                        <svg class="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `).join('');
        }

        function renderMemories(filteredMemories = memories) {
            const container = document.getElementById('memoryItems');
            document.getElementById('memoryCount').textContent = filteredMemories.length;
            
            if (filteredMemories.length === 0) {
                container.innerHTML = `
                    <div class="text-center text-gray-400 mt-8">
                        <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        <p class="text-sm">No memories found</p>
                        <p class="text-xs mt-1">Start browsing to build memories</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredMemories.map(item => `
                <div class="p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition-colors group">
                    <div class="text-sm text-white mb-2 line-clamp-3">
                        ${item.content}
                    </div>
                    <div class="flex items-center gap-2 text-xs text-gray-400 mb-2">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>${formatTimeAgo(item.timestamp)}</span>
                        <span class="ml-auto">${Math.round(item.relevance * 100)}% match</span>
                    </div>
                    <div class="flex flex-wrap gap-1">
                        ${item.tags.slice(0, 3).map(tag => `
                            <span class="inline-flex items-center gap-1 px-2 py-1 bg-gray-600 text-gray-300 rounded text-xs">
                                <svg class="w-2 h-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                </svg>
                                ${tag}
                            </span>
                        `).join('')}
                        ${item.tags.length > 3 ? `<span class="text-xs text-gray-400">+${item.tags.length - 3} more</span>` : ''}
                    </div>
                </div>
            `).join('');
        }

        function formatTimeAgo(date) {
            const now = new Date();
            const diffMs = now - date;
            const diffMins = Math.floor(diffMs / (1000 * 60));
            const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
            const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

            if (diffMins < 1) return "Just now";
            if (diffMins < 60) return `${diffMins}m ago`;
            if (diffHours < 24) return `${diffHours}h ago`;
            return `${diffDays}d ago`;
        }

        function createNewTab() {
            const newTab = {
                id: Date.now(),
                title: "New Tab",
                url: "memori://newtab",
                isActive: false
            };
            tabs.forEach(tab => tab.isActive = false);
            newTab.isActive = true;
            tabs.push(newTab);
            activeTabId = newTab.id;
            renderTabs();
            document.getElementById('urlBar').value = newTab.url;
        }

        function closeTab(event, tabId) {
            event.stopPropagation();
            if (tabs.length === 1) return; // Don't close last tab
            
            tabs = tabs.filter(tab => tab.id !== tabId);
            if (activeTabId === tabId) {
                tabs[0].isActive = true;
                activeTabId = tabs[0].id;
                document.getElementById('urlBar').value = tabs[0].url;
            }
            renderTabs();
        }

        function switchTab(tabId) {
            tabs.forEach(tab => {
                tab.isActive = tab.id === tabId;
            });
            activeTabId = tabId;
            const activeTab = tabs.find(tab => tab.id === tabId);
            document.getElementById('urlBar').value = activeTab.url;
            renderTabs();
        }

        function handleSearch(event) {
            const query = event.target.value;
            if (event.key === 'Enter' && query.trim()) {
                // Simulate AI search
                console.log('🧠 AI Search:', query);
                // Add to memory
                memories.unshift({
                    id: Date.now(),
                    content: `Search query: "${query}" - AI-enhanced results and suggestions`,
                    timestamp: new Date(),
                    tags: ["search", "ai", "query"],
                    relevance: 1.0
                });
                renderMemories();
            }
        }

        function searchMemory(event) {
            const query = event.target.value.toLowerCase();
            if (!query.trim()) {
                renderMemories();
                return;
            }
            
            const filtered = memories.filter(memory => 
                memory.content.toLowerCase().includes(query) ||
                memory.tags.some(tag => tag.toLowerCase().includes(query))
            );
            renderMemories(filtered);
        }

        function toggleVoiceSearch() {
            isVoiceListening = !isVoiceListening;
            const btn = document.getElementById('voiceBtn');
            if (isVoiceListening) {
                btn.classList.add('bg-red-500', 'text-white');
                btn.classList.remove('bg-gray-700', 'text-gray-300');
                console.log('🎤 Voice search started');
            } else {
                btn.classList.remove('bg-red-500', 'text-white');
                btn.classList.add('bg-gray-700', 'text-gray-300');
                console.log('🎤 Voice search stopped');
            }
        }

        function saveCurrentPage() {
            const activeTab = tabs.find(tab => tab.isActive);
            memories.unshift({
                id: Date.now(),
                content: `Saved page: ${activeTab.title} - ${activeTab.url}`,
                timestamp: new Date(),
                tags: ["saved", "webpage", "bookmark"],
                relevance: 1.0
            });
            renderMemories();
            console.log('💾 Page saved to memory');
        }

        function goBack() { console.log('⬅️ Going back'); }
        function goForward() { console.log('➡️ Going forward'); }
        function refresh() { console.log('🔄 Refreshing'); }
        function goHome() { 
            document.getElementById('urlBar').value = 'memori://newtab';
            console.log('🏠 Going home'); 
        }

        function handleUrlInput(event) {
            if (event.key === 'Enter') {
                const url = event.target.value;
                console.log('🌐 Navigating to:', url);
                const activeTab = tabs.find(tab => tab.isActive);
                if (activeTab) {
                    activeTab.url = url;
                    activeTab.title = url.includes('://') ? new URL(url).hostname : url;
                    renderTabs();
                }
            }
        }

        // Initialize the demo
        renderTabs();
        renderMemories();
        
        console.log('🧠 Memori Browser Demo Ready!');
        console.log('✨ Features working:');
        console.log('  • Tab management');
        console.log('  • Memory search');
        console.log('  • AI-enhanced search');
        console.log('  • Voice search (UI only)');
        console.log('  • Page saving');
    </script>
</body>
</html>
