{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 4970794001433897701, "deps": [[2671782512663819132, "tauri_utils", false, 13635082913196281338], [4899080583175475170, "semver", false, 10885335960225221361], [6913375703034175521, "schemars", false, 9940543878405923545], [7170110829644101142, "json_patch", false, 7155364116303564817], [9689903380558560274, "serde", false, 3158597207148056356], [12714016054753183456, "tauri_winres", false, 2100632826943273140], [13077543566650298139, "heck", false, 11003689655865491247], [13475171727366188400, "cargo_toml", false, 4482357053871384480], [13625485746686963219, "anyhow", false, 6404980772165172830], [15367738274754116744, "serde_json", false, 16756031453236183760], [15609422047640926750, "toml", false, 16415255170703270083], [15622660310229662834, "walkdir", false, 17524825054824304106], [16928111194414003569, "dirs", false, 18426629402993113980], [17155886227862585100, "glob", false, 11939270752987892944]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-814770f2a0b35b7c\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}