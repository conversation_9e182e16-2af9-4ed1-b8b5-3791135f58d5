import React, { useEffect } from "react";
import { <PERSON>rowserView } from "./components/BrowserView";
import { TabBar } from "./components/TabBar";
import { SearchBar } from "./components/SearchBar";
import { MemoryPanel } from "./components/MemoryPanel";

function App() {
  useEffect(() => {
    // Initialize the app
    console.log("🧠 Memori Browser starting...");
  }, []);

  return (
    <div className="h-screen flex flex-col bg-gray-900 text-white overflow-hidden">
      <div className="flex-none">
        <SearchBar />
        <TabBar />
      </div>
      <div className="flex-1 flex min-h-0">
        <div className="flex-1 min-w-0">
          <BrowserView />
        </div>
        <div className="w-80 border-l border-gray-700 flex-shrink-0">
          <MemoryPanel />
        </div>
      </div>
    </div>
  );
}

export default App;
