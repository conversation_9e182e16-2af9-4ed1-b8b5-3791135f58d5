// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{Manager, SystemTray, SystemTrayEvent, SystemTrayMenu, CustomMenuItem};
use std::sync::Arc;
use tokio::sync::Mutex;

mod memory;
mod ai;
mod search;
mod browser;
mod utils;

use memory::MemoryEngine;
use ai::AIEngine;
use search::SearchEngine;
use browser::BrowserEngine;

/// Application state that will be shared across the app
#[derive(Debug)]
pub struct AppState {
    pub memory: Arc<Mutex<MemoryEngine>>,
    pub ai: Arc<Mutex<AIEngine>>,
    pub search: Arc<Mutex<SearchEngine>>,
    pub browser: Arc<Mutex<BrowserEngine>>,
}

impl AppState {
    pub async fn new() -> Result<Self, Box<dyn std::error::Error>> {
        let memory = Arc::new(Mutex::new(MemoryEngine::new().await?));
        let ai = Arc::new(Mutex::new(AIEngine::new().await?));
        let search = Arc::new(Mutex::new(SearchEngine::new().await?));
        let browser = Arc::new(Mutex::new(BrowserEngine::new().await?));

        Ok(Self {
            memory,
            ai,
            search,
            browser,
        })
    }
}

// Tauri commands that can be called from the frontend
#[tauri::command]
async fn search_tabs(
    query: String,
    state: tauri::State<'_, AppState>,
) -> Result<Vec<browser::Tab>, String> {
    let search = state.search.lock().await;
    let browser = state.browser.lock().await;
    
    search.search_tabs(&query, &browser.get_tabs())
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_ai_suggestion(
    context: String,
    state: tauri::State<'_, AppState>,
) -> Result<String, String> {
    let ai = state.ai.lock().await;
    ai.get_suggestion(&context)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn save_memory(
    content: String,
    metadata: serde_json::Value,
    state: tauri::State<'_, AppState>,
) -> Result<String, String> {
    let memory = state.memory.lock().await;
    memory.save_memory(&content, metadata)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn search_memory(
    query: String,
    limit: Option<usize>,
    state: tauri::State<'_, AppState>,
) -> Result<Vec<memory::MemoryItem>, String> {
    let memory = state.memory.lock().await;
    memory.search(&query, limit.unwrap_or(10))
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn create_tab(
    url: String,
    state: tauri::State<'_, AppState>,
) -> Result<browser::Tab, String> {
    let mut browser = state.browser.lock().await;
    browser.create_tab(&url)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn close_tab(
    tab_id: String,
    state: tauri::State<'_, AppState>,
) -> Result<(), String> {
    let mut browser = state.browser.lock().await;
    browser.close_tab(&tab_id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_tabs(
    state: tauri::State<'_, AppState>,
) -> Result<Vec<browser::Tab>, String> {
    let browser = state.browser.lock().await;
    Ok(browser.get_tabs())
}

#[tauri::command]
async fn switch_tab(
    tab_id: String,
    state: tauri::State<'_, AppState>,
) -> Result<(), String> {
    let mut browser = state.browser.lock().await;
    browser.switch_tab(&tab_id)
        .await
        .map_err(|e| e.to_string())
}

fn create_system_tray() -> SystemTray {
    let quit = CustomMenuItem::new("quit".to_string(), "Quit Memori");
    let show = CustomMenuItem::new("show".to_string(), "Show Memori");
    let new_tab = CustomMenuItem::new("new_tab".to_string(), "New Tab");
    
    let tray_menu = SystemTrayMenu::new()
        .add_item(show)
        .add_item(new_tab)
        .add_native_item(tauri::SystemTrayMenuItem::Separator)
        .add_item(quit);

    SystemTray::new().with_menu(tray_menu)
}

fn handle_system_tray_event(app: &tauri::AppHandle, event: SystemTrayEvent) {
    match event {
        SystemTrayEvent::LeftClick {
            position: _,
            size: _,
            ..
        } => {
            let window = app.get_window("main").unwrap();
            window.show().unwrap();
            window.set_focus().unwrap();
        }
        SystemTrayEvent::MenuItemClick { id, .. } => match id.as_str() {
            "quit" => {
                std::process::exit(0);
            }
            "show" => {
                let window = app.get_window("main").unwrap();
                window.show().unwrap();
                window.set_focus().unwrap();
            }
            "new_tab" => {
                let window = app.get_window("main").unwrap();
                window.emit("new-tab", {}).unwrap();
            }
            _ => {}
        },
        _ => {}
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize logging
    env_logger::init();
    
    log::info!("Starting Memori Browser...");

    // Initialize application state
    let app_state = AppState::new().await?;
    
    log::info!("Application state initialized");

    tauri::Builder::default()
        .manage(app_state)
        .system_tray(create_system_tray())
        .on_system_tray_event(handle_system_tray_event)
        .invoke_handler(tauri::generate_handler![
            search_tabs,
            get_ai_suggestion,
            save_memory,
            search_memory,
            create_tab,
            close_tab,
            get_tabs,
            switch_tab
        ])
        .setup(|app| {
            let window = app.get_window("main").unwrap();
            
            // Set up global shortcuts
            let app_handle = app.handle();
            app.global_shortcut_manager()
                .register("CmdOrCtrl+Shift+Space", move || {
                    let window = app_handle.get_window("main").unwrap();
                    if window.is_visible().unwrap() {
                        window.hide().unwrap();
                    } else {
                        window.show().unwrap();
                        window.set_focus().unwrap();
                    }
                })
                .unwrap();

            // Set window properties for optimal performance
            #[cfg(debug_assertions)]
            window.open_devtools();

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");

    Ok(())
}
