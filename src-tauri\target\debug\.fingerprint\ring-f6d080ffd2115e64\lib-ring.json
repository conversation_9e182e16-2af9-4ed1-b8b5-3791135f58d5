{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\", \"dev_urandom_fallback\"]", "declared_features": "[\"alloc\", \"default\", \"dev_urandom_fallback\", \"less-safe-getrandom-custom-or-rdrand\", \"less-safe-getrandom-espidf\", \"slow_tests\", \"std\", \"test_logging\", \"unstable-testing-arm-no-hw\", \"unstable-testing-arm-no-neon\", \"wasm32_unknown_unknown_js\"]", "target": 13947150742743679355, "profile": 10809724437792986082, "path": 7558831532039610132, "deps": [[2828590642173593838, "cfg_if", false, 12159319681757695495], [5491919304041016563, "build_script_build", false, 3333228099549721082], [8995469080876806959, "untrusted", false, 6313376460096166398], [9920160576179037441, "getrandom", false, 7755069163356586665]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\ring-f6d080ffd2115e64\\dep-lib-ring", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}