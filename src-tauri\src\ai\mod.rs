use serde::{Deserialize, Serialize};
use anyhow::{Result, Context};
use reqwest::Client;
use std::collections::HashMap;

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIConfig {
    pub openai_api_key: Option<String>,
    pub claude_api_key: Option<String>,
    pub gemini_api_key: Option<String>,
    pub default_provider: String,
    pub local_model_path: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AIRequest {
    pub prompt: String,
    pub context: Option<String>,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIResponse {
    pub content: String,
    pub provider: String,
    pub tokens_used: Option<u32>,
    pub latency_ms: u64,
}

/// Lightweight AI engine with multiple provider support
#[derive(Debug)]
pub struct AIEngine {
    config: AIConfig,
    client: Client,
}

impl AIEngine {
    pub async fn new() -> Result<Self> {
        let config = AIConfig {
            openai_api_key: std::env::var("OPENAI_API_KEY").ok(),
            claude_api_key: std::env::var("ANTHROPIC_API_KEY").ok(),
            gemini_api_key: std::env::var("GEMINI_API_KEY").ok(),
            default_provider: "local".to_string(), // Start with local for privacy
            local_model_path: None,
        };

        let client = Client::new();

        Ok(Self { config, client })
    }

    /// Get AI suggestion based on context
    pub async fn get_suggestion(&self, context: &str) -> Result<String> {
        let request = AIRequest {
            prompt: format!("Based on this browsing context, provide a helpful suggestion: {}", context),
            context: Some(context.to_string()),
            max_tokens: Some(150),
            temperature: Some(0.7),
        };

        let response = self.process_request(request).await?;
        Ok(response.content)
    }

    /// Summarize content
    pub async fn summarize_content(&self, content: &str) -> Result<String> {
        let request = AIRequest {
            prompt: format!("Summarize this content in 2-3 sentences: {}", content),
            context: None,
            max_tokens: Some(100),
            temperature: Some(0.3),
        };

        let response = self.process_request(request).await?;
        Ok(response.content)
    }

    /// Generate tags for content
    pub async fn generate_tags(&self, content: &str) -> Result<Vec<String>> {
        let request = AIRequest {
            prompt: format!("Generate 3-5 relevant tags for this content (comma-separated): {}", content),
            context: None,
            max_tokens: Some(50),
            temperature: Some(0.5),
        };

        let response = self.process_request(request).await?;
        let tags: Vec<String> = response.content
            .split(',')
            .map(|s| s.trim().to_lowercase())
            .filter(|s| !s.is_empty())
            .collect();

        Ok(tags)
    }

    /// Process AI request with fallback providers
    async fn process_request(&self, request: AIRequest) -> Result<AIResponse> {
        let start_time = std::time::Instant::now();

        // Try local model first (mock for now)
        if self.config.default_provider == "local" {
            let response = self.process_local_request(&request).await?;
            return Ok(AIResponse {
                content: response,
                provider: "local".to_string(),
                tokens_used: Some(request.max_tokens.unwrap_or(100)),
                latency_ms: start_time.elapsed().as_millis() as u64,
            });
        }

        // Fallback to cloud providers
        if let Some(_api_key) = &self.config.openai_api_key {
            match self.process_openai_request(&request).await {
                Ok(response) => return Ok(AIResponse {
                    content: response,
                    provider: "openai".to_string(),
                    tokens_used: request.max_tokens,
                    latency_ms: start_time.elapsed().as_millis() as u64,
                }),
                Err(e) => log::warn!("OpenAI request failed: {}", e),
            }
        }

        // Default fallback response
        Ok(AIResponse {
            content: "AI service temporarily unavailable. Please try again later.".to_string(),
            provider: "fallback".to_string(),
            tokens_used: Some(10),
            latency_ms: start_time.elapsed().as_millis() as u64,
        })
    }

    /// Process request with local model (mock implementation)
    async fn process_local_request(&self, request: &AIRequest) -> Result<String> {
        // Mock local AI responses for now
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        if request.prompt.contains("suggestion") {
            return Ok("Based on your browsing pattern, you might want to check related documentation or tutorials.".to_string());
        }

        if request.prompt.contains("Summarize") {
            return Ok("This content discusses key concepts and provides practical examples for implementation.".to_string());
        }

        if request.prompt.contains("tags") {
            return Ok("programming, tutorial, documentation, reference, guide".to_string());
        }

        Ok("I understand your request. Here's a helpful response based on the context provided.".to_string())
    }

    /// Process request with OpenAI API
    async fn process_openai_request(&self, request: &AIRequest) -> Result<String> {
        let api_key = self.config.openai_api_key
            .as_ref()
            .context("OpenAI API key not configured")?;

        let mut payload = HashMap::new();
        payload.insert("model", "gpt-3.5-turbo".to_string());
        payload.insert("max_tokens", request.max_tokens.unwrap_or(150).to_string());
        payload.insert("temperature", request.temperature.unwrap_or(0.7).to_string());

        let messages = vec![
            HashMap::from([
                ("role".to_string(), "user".to_string()),
                ("content".to_string(), request.prompt.clone()),
            ])
        ];
        payload.insert("messages", serde_json::to_string(&messages)?);

        let response = self.client
            .post("https://api.openai.com/v1/chat/completions")
            .header("Authorization", format!("Bearer {}", api_key))
            .header("Content-Type", "application/json")
            .json(&payload)
            .send()
            .await?;

        if !response.status().is_success() {
            return Err(anyhow::anyhow!("OpenAI API request failed: {}", response.status()));
        }

        let response_json: serde_json::Value = response.json().await?;
        let content = response_json["choices"][0]["message"]["content"]
            .as_str()
            .unwrap_or("No response generated")
            .to_string();

        Ok(content)
    }

    /// Update AI configuration
    pub fn update_config(&mut self, config: AIConfig) {
        self.config = config;
    }

    /// Get current configuration
    pub fn get_config(&self) -> &AIConfig {
        &self.config
    }

    /// Check if AI is available
    pub fn is_available(&self) -> bool {
        self.config.openai_api_key.is_some() || 
        self.config.claude_api_key.is_some() || 
        self.config.gemini_api_key.is_some() ||
        self.config.default_provider == "local"
    }

    /// Get AI status
    pub async fn get_status(&self) -> serde_json::Value {
        serde_json::json!({
            "available": self.is_available(),
            "default_provider": self.config.default_provider,
            "providers": {
                "local": true,
                "openai": self.config.openai_api_key.is_some(),
                "claude": self.config.claude_api_key.is_some(),
                "gemini": self.config.gemini_api_key.is_some(),
            }
        })
    }
}
