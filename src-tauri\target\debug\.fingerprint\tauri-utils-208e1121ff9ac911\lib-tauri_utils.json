{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 10809724437792986082, "path": 2457740146775615380, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 17450301914574925497], [3150220818285335163, "url", false, 3125953265746058012], [3191507132440681679, "serde_untagged", false, 15309373307402890729], [4071963112282141418, "serde_with", false, 1195935860072441529], [4899080583175475170, "semver", false, 11798332855694283673], [5986029879202738730, "log", false, 3050089541774172585], [6606131838865521726, "ctor", false, 5074607611391229500], [7170110829644101142, "json_patch", false, 7747423815637989448], [8319709847752024821, "uuid", false, 6349249694372189590], [9010263965687315507, "http", false, 11529510996351137805], [9451456094439810778, "regex", false, 13538468039916519165], [9556762810601084293, "brotli", false, 5789057620143175693], [9689903380558560274, "serde", false, 6629062398223269867], [10806645703491011684, "thiserror", false, 6290649561672044865], [11989259058781683633, "dunce", false, 7572848423196634073], [13625485746686963219, "anyhow", false, 16855437522592994028], [15367738274754116744, "serde_json", false, 7297581080823534964], [15609422047640926750, "toml", false, 2356268943739563878], [15622660310229662834, "walkdir", false, 3515628484365807626], [15932120279885307830, "memchr", false, 1932448928200586278], [17146114186171651583, "infer", false, 13477904038799502471], [17155886227862585100, "glob", false, 7557466002335538042], [17186037756130803222, "phf", false, 13557055953029605768]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-208e1121ff9ac911\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}