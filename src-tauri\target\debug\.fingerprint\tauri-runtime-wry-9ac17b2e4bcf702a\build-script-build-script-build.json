{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"devtools\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 11970886654178176200, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-9ac17b2e4bcf702a\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}