{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 2225463790103693989, "path": 14993654793266126027, "deps": [[5103565458935487, "futures_io", false, 6323241888413343294], [40386456601120721, "percent_encoding", false, 7157723091141506790], [530211389790465181, "hex", false, 9508896561565191631], [788558663644978524, "crossbeam_queue", false, 2233159061335169440], [966925859616469517, "ahash", false, 17032900957029264562], [1162433738665300155, "crc", false, 2546127529398922724], [1464803193346256239, "event_listener", false, 1190100956394591598], [1811549171721445101, "futures_channel", false, 8982748945083406494], [3150220818285335163, "url", false, 5650291493764149010], [3405817021026194662, "hashlink", false, 10826857309130582385], [3646857438214563691, "futures_intrusive", false, 5983537980168046513], [3666196340704888985, "smallvec", false, 13960693664870852389], [3712811570531045576, "byteorder", false, 898647821409535825], [3722963349756955755, "once_cell", false, 1128676604584301], [5986029879202738730, "log", false, 1134941415235641757], [6493259146304816786, "indexmap", false, 6951751308797062562], [7620660491849607393, "futures_core", false, 3830813326361008461], [8008191657135824715, "thiserror", false, 16403970322113305718], [8319709847752024821, "uuid", false, 2416734466083964470], [8606274917505247608, "tracing", false, 17794938710794229538], [9689903380558560274, "serde", false, 3158597207148056356], [9857275760291862238, "sha2", false, 7000312947546632887], [9897246384292347999, "chrono", false, 17904666745772013261], [10629569228670356391, "futures_util", false, 13934363597799898367], [10862088793507253106, "sqlformat", false, 18214739767052152309], [11295624341523567602, "rustls", false, 2732976899220533618], [12170264697963848012, "either", false, 1332962060075666494], [12393800526703971956, "tokio", false, 3833170481708910760], [15367738274754116744, "serde_json", false, 16756031453236183760], [15932120279885307830, "memchr", false, 9973787961473383461], [16066129441945555748, "bytes", false, 11555769161521176802], [16311359161338405624, "rustls_pemfile", false, 18354467492915664897], [16973251432615581304, "tokio_stream", false, 536444000933225089], [17106256174509013259, "atoi", false, 13071358764518525647], [17605717126308396068, "paste", false, 3230105283163785482], [17652733826348741533, "webpki_roots", false, 11220414750014203476]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-core-4524c94dbe1b0c4c\\dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}