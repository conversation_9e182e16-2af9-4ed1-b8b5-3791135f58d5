{"rustc": 1842507548689473721, "features": "[\"__rustls\", \"__tls\", \"default\", \"default-tls\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"rustls\", \"rustls-tls\", \"rustls-tls-webpki-roots\", \"serde_json\", \"tokio-native-tls\", \"tokio-rustls\", \"webpki-roots\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 10809724437792986082, "path": 2235758518937167739, "deps": [[40386456601120721, "percent_encoding", false, 17602416279374786006], [95042085696191081, "ipnet", false, 7232923361772495118], [264090853244900308, "sync_wrapper", false, 15522154492782220552], [784494742817713399, "tower_service", false, 4400704444874605650], [1044435446100926395, "hyper_rustls", false, 11595739560327219151], [1906322745568073236, "pin_project_lite", false, 5316314698746330434], [3150220818285335163, "url", false, 3125953265746058012], [3722963349756955755, "once_cell", false, 1443146423828032470], [4405182208873388884, "http", false, 6904678028112442955], [5986029879202738730, "log", false, 3050089541774172585], [7414427314941361239, "hyper", false, 8296148971750639400], [7620660491849607393, "futures_core", false, 15648460492167422919], [8405603588346937335, "winreg", false, 15785824054220409649], [8915503303801890683, "http_body", false, 10059608713620019792], [9689903380558560274, "serde", false, 6629062398223269867], [10229185211513642314, "mime", false, 871270052809719969], [10629569228670356391, "futures_util", false, 2130271602361178218], [11295624341523567602, "rustls", false, 3284958607360263734], [12186126227181294540, "tokio_native_tls", false, 6191894899847345670], [12367227501898450486, "hyper_tls", false, 11888653059708044092], [12393800526703971956, "tokio", false, 4564033278980346352], [13809605890706463735, "h2", false, 17958087555166080497], [14564311161534545801, "encoding_rs", false, 5039768823267832287], [15367738274754116744, "serde_json", false, 7297581080823534964], [16066129441945555748, "bytes", false, 15851513111714293082], [16311359161338405624, "rustls_pemfile", false, 6820821265403460970], [16542808166767769916, "serde_urlencoded", false, 9944575196005845742], [16622232390123975175, "tokio_rustls", false, 7611513259505373695], [16785601910559813697, "native_tls_crate", false, 13770774843257500839], [17652733826348741533, "webpki_roots", false, 17307823372150873358], [18066890886671768183, "base64", false, 13177702509137425355]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\reqwest-e2509939c3573268\\dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}