use serde::{Deserialize, Serialize};
use sqlx::{SqlitePool, Row};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use anyhow::{Result, Context};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MemoryItem {
    pub id: String,
    pub content: String,
    pub metadata: serde_json::Value,
    pub embedding: Option<Vec<f32>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub access_count: i64,
    pub last_accessed: DateTime<Utc>,
    pub tags: Vec<String>,
    pub relevance_score: Option<f32>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryQuery {
    pub text: String,
    pub filters: Option<HashMap<String, serde_json::Value>>,
    pub limit: usize,
    pub threshold: f32,
}

/// Lightweight memory engine with SQLite storage
#[derive(Debug)]
pub struct MemoryEngine {
    db: SqlitePool,
}

impl MemoryEngine {
    pub async fn new() -> Result<Self> {
        let db_path = dirs::data_dir()
            .context("Failed to get data directory")?
            .join("memori")
            .join("memory.db");

        // Ensure directory exists
        if let Some(parent) = db_path.parent() {
            tokio::fs::create_dir_all(parent).await?;
        }

        let database_url = format!("sqlite:{}", db_path.display());
        let db = SqlitePool::connect(&database_url).await?;

        // Create tables if they don't exist
        sqlx::query!(
            r#"
            CREATE TABLE IF NOT EXISTS memory_items (
                id TEXT PRIMARY KEY,
                content TEXT NOT NULL,
                metadata TEXT NOT NULL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                access_count INTEGER DEFAULT 0,
                last_accessed TEXT NOT NULL,
                tags TEXT NOT NULL
            )
            "#
        )
        .execute(&db)
        .await?;

        // Create index for faster searches
        sqlx::query!(
            "CREATE INDEX IF NOT EXISTS idx_memory_content ON memory_items(content)"
        )
        .execute(&db)
        .await?;

        sqlx::query!(
            "CREATE INDEX IF NOT EXISTS idx_memory_access ON memory_items(access_count DESC, last_accessed DESC)"
        )
        .execute(&db)
        .await?;

        Ok(Self { db })
    }

    /// Save content to memory
    pub async fn save_memory(
        &self,
        content: &str,
        metadata: serde_json::Value,
    ) -> Result<String> {
        let id = Uuid::new_v4().to_string();
        let now = Utc::now();

        // Extract tags from metadata or content
        let tags = self.extract_tags(content, &metadata);
        let tags_json = serde_json::to_string(&tags)?;
        let metadata_json = serde_json::to_string(&metadata)?;

        sqlx::query!(
            r#"
            INSERT INTO memory_items (
                id, content, metadata, created_at, updated_at,
                access_count, last_accessed, tags
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            "#,
            id,
            content,
            metadata_json,
            now.to_rfc3339(),
            now.to_rfc3339(),
            0,
            now.to_rfc3339(),
            tags_json
        )
        .execute(&self.db)
        .await?;

        log::info!("Saved memory item: {}", id);
        Ok(id)
    }

    /// Search memory using text matching
    pub async fn search(&self, query: &str, limit: usize) -> Result<Vec<MemoryItem>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM memory_items
            WHERE content LIKE ? OR metadata LIKE ?
            ORDER BY access_count DESC, last_accessed DESC
            LIMIT ?
            "#,
            format!("%{}%", query),
            format!("%{}%", query),
            limit as i64
        )
        .fetch_all(&self.db)
        .await?;

        let mut items = Vec::new();
        for row in rows {
            let tags: Vec<String> = serde_json::from_str(&row.tags)?;
            let metadata: serde_json::Value = serde_json::from_str(&row.metadata)?;
            let created_at = DateTime::parse_from_rfc3339(&row.created_at)?.with_timezone(&Utc);
            let updated_at = DateTime::parse_from_rfc3339(&row.updated_at)?.with_timezone(&Utc);
            let last_accessed = DateTime::parse_from_rfc3339(&row.last_accessed)?.with_timezone(&Utc);

            items.push(MemoryItem {
                id: row.id,
                content: row.content,
                metadata,
                embedding: None,
                created_at,
                updated_at,
                access_count: row.access_count,
                last_accessed,
                tags,
                relevance_score: Some(0.8),
            });
        }

        // Update access counts
        for item in &items {
            self.update_access_count(&item.id).await?;
        }

        Ok(items)
    }



    /// Update access count and last accessed time
    async fn update_access_count(&self, id: &str) -> Result<()> {
        let now = Utc::now();
        sqlx::query!(
            "UPDATE memory_items SET access_count = access_count + 1, last_accessed = ? WHERE id = ?",
            now.to_rfc3339(),
            id
        )
        .execute(&self.db)
        .await?;
        Ok(())
    }

    /// Extract tags from content and metadata
    fn extract_tags(&self, content: &str, metadata: &serde_json::Value) -> Vec<String> {
        let mut tags = Vec::new();

        // Extract from metadata
        if let Some(meta_tags) = metadata.get("tags").and_then(|t| t.as_array()) {
            for tag in meta_tags {
                if let Some(tag_str) = tag.as_str() {
                    tags.push(tag_str.to_string());
                }
            }
        }

        // Extract from URL if present
        if let Some(url) = metadata.get("url").and_then(|u| u.as_str()) {
            if let Ok(parsed_url) = url::Url::parse(url) {
                if let Some(domain) = parsed_url.domain() {
                    tags.push(format!("domain:{}", domain));
                }
            }
        }

        // Extract from content type
        if let Some(content_type) = metadata.get("type").and_then(|t| t.as_str()) {
            tags.push(format!("type:{}", content_type));
        }

        tags
    }



    /// Get memory statistics
    pub async fn get_stats(&self) -> Result<serde_json::Value> {
        let total_items = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM memory_items"
        )
        .fetch_one(&self.db)
        .await?;

        let total_accesses = sqlx::query_scalar!(
            "SELECT SUM(access_count) FROM memory_items"
        )
        .fetch_one(&self.db)
        .await?;

        Ok(serde_json::json!({
            "total_items": total_items,
            "total_accesses": total_accesses.unwrap_or(0),
        }))
    }
}
