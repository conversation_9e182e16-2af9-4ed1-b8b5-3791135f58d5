use serde::{Deserialize, Serialize};
use sqlx::{SqlitePool, Row};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use anyhow::{Result, Context};

pub mod vector_store;
pub mod embeddings;

use vector_store::VectorStore;
use embeddings::EmbeddingEngine;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MemoryItem {
    pub id: String,
    pub content: String,
    pub metadata: serde_json::Value,
    pub embedding: Option<Vec<f32>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub access_count: i64,
    pub last_accessed: DateTime<Utc>,
    pub tags: Vec<String>,
    pub relevance_score: Option<f32>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct MemoryQuery {
    pub text: String,
    pub filters: Option<HashMap<String, serde_json::Value>>,
    pub limit: usize,
    pub threshold: f32,
}

/// High-performance memory engine with SQLite + vector search
#[derive(Debug)]
pub struct MemoryEngine {
    db: SqlitePool,
    vector_store: VectorStore,
    embedding_engine: EmbeddingEngine,
}

impl MemoryEngine {
    pub async fn new() -> Result<Self> {
        let db_path = dirs::data_dir()
            .context("Failed to get data directory")?
            .join("memori")
            .join("memory.db");
        
        // Ensure directory exists
        if let Some(parent) = db_path.parent() {
            tokio::fs::create_dir_all(parent).await?;
        }

        let database_url = format!("sqlite:{}", db_path.display());
        let db = SqlitePool::connect(&database_url).await?;
        
        // Run migrations
        sqlx::migrate!("./migrations").run(&db).await?;
        
        let vector_store = VectorStore::new().await?;
        let embedding_engine = EmbeddingEngine::new().await?;

        Ok(Self {
            db,
            vector_store,
            embedding_engine,
        })
    }

    /// Save content to memory with automatic embedding generation
    pub async fn save_memory(
        &self,
        content: &str,
        metadata: serde_json::Value,
    ) -> Result<String> {
        let id = Uuid::new_v4().to_string();
        let now = Utc::now();
        
        // Generate embedding for the content
        let embedding = self.embedding_engine.generate_embedding(content).await?;
        
        // Extract tags from metadata or content
        let tags = self.extract_tags(content, &metadata);
        let tags_json = serde_json::to_value(&tags)?;
        
        // Save to SQLite
        sqlx::query!(
            r#"
            INSERT INTO memory_items (
                id, content, metadata, created_at, updated_at, 
                access_count, last_accessed, tags
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            "#,
            id,
            content,
            metadata,
            now,
            now,
            0,
            now,
            tags_json
        )
        .execute(&self.db)
        .await?;

        // Save to vector store
        self.vector_store.insert(&id, &embedding, content, &metadata).await?;

        log::info!("Saved memory item: {}", id);
        Ok(id)
    }

    /// Search memory using semantic similarity
    pub async fn search(&self, query: &str, limit: usize) -> Result<Vec<MemoryItem>> {
        // Generate embedding for query
        let query_embedding = self.embedding_engine.generate_embedding(query).await?;
        
        // Search vector store
        let vector_results = self.vector_store
            .search(&query_embedding, limit * 2) // Get more results for reranking
            .await?;

        // Get full items from SQLite
        let mut items = Vec::new();
        for result in vector_results {
            if let Ok(item) = self.get_memory_item(&result.id).await {
                let mut item = item;
                item.relevance_score = Some(result.score);
                items.push(item);
            }
        }

        // Also do text-based search for exact matches
        let text_results = self.text_search(query, limit).await?;
        
        // Merge and deduplicate results
        let mut combined_results = self.merge_search_results(items, text_results);
        
        // Sort by relevance and limit
        combined_results.sort_by(|a, b| {
            b.relevance_score
                .unwrap_or(0.0)
                .partial_cmp(&a.relevance_score.unwrap_or(0.0))
                .unwrap_or(std::cmp::Ordering::Equal)
        });
        
        combined_results.truncate(limit);
        
        // Update access counts
        for item in &combined_results {
            self.update_access_count(&item.id).await?;
        }

        Ok(combined_results)
    }

    /// Get a specific memory item by ID
    pub async fn get_memory_item(&self, id: &str) -> Result<MemoryItem> {
        let row = sqlx::query!(
            "SELECT * FROM memory_items WHERE id = ?",
            id
        )
        .fetch_one(&self.db)
        .await?;

        let tags: Vec<String> = serde_json::from_value(row.tags)?;

        Ok(MemoryItem {
            id: row.id,
            content: row.content,
            metadata: row.metadata,
            embedding: None, // Don't load embedding unless needed
            created_at: row.created_at,
            updated_at: row.updated_at,
            access_count: row.access_count,
            last_accessed: row.last_accessed,
            tags,
            relevance_score: None,
        })
    }

    /// Text-based search using SQLite FTS
    async fn text_search(&self, query: &str, limit: usize) -> Result<Vec<MemoryItem>> {
        let rows = sqlx::query!(
            r#"
            SELECT * FROM memory_items 
            WHERE content LIKE ? OR json_extract(metadata, '$.title') LIKE ?
            ORDER BY access_count DESC, last_accessed DESC
            LIMIT ?
            "#,
            format!("%{}%", query),
            format!("%{}%", query),
            limit as i64
        )
        .fetch_all(&self.db)
        .await?;

        let mut items = Vec::new();
        for row in rows {
            let tags: Vec<String> = serde_json::from_value(row.tags)?;
            items.push(MemoryItem {
                id: row.id,
                content: row.content,
                metadata: row.metadata,
                embedding: None,
                created_at: row.created_at,
                updated_at: row.updated_at,
                access_count: row.access_count,
                last_accessed: row.last_accessed,
                tags,
                relevance_score: Some(0.8), // High relevance for exact text matches
            });
        }

        Ok(items)
    }

    /// Update access count and last accessed time
    async fn update_access_count(&self, id: &str) -> Result<()> {
        let now = Utc::now();
        sqlx::query!(
            "UPDATE memory_items SET access_count = access_count + 1, last_accessed = ? WHERE id = ?",
            now,
            id
        )
        .execute(&self.db)
        .await?;
        Ok(())
    }

    /// Extract tags from content and metadata
    fn extract_tags(&self, content: &str, metadata: &serde_json::Value) -> Vec<String> {
        let mut tags = Vec::new();
        
        // Extract from metadata
        if let Some(meta_tags) = metadata.get("tags").and_then(|t| t.as_array()) {
            for tag in meta_tags {
                if let Some(tag_str) = tag.as_str() {
                    tags.push(tag_str.to_string());
                }
            }
        }
        
        // Extract from URL if present
        if let Some(url) = metadata.get("url").and_then(|u| u.as_str()) {
            if let Ok(parsed_url) = url::Url::parse(url) {
                if let Some(domain) = parsed_url.domain() {
                    tags.push(format!("domain:{}", domain));
                }
            }
        }
        
        // Extract from content type
        if let Some(content_type) = metadata.get("type").and_then(|t| t.as_str()) {
            tags.push(format!("type:{}", content_type));
        }
        
        tags
    }

    /// Merge and deduplicate search results
    fn merge_search_results(
        &self,
        vector_results: Vec<MemoryItem>,
        text_results: Vec<MemoryItem>,
    ) -> Vec<MemoryItem> {
        let mut combined = HashMap::new();
        
        // Add vector results
        for item in vector_results {
            combined.insert(item.id.clone(), item);
        }
        
        // Add text results, preferring higher relevance scores
        for item in text_results {
            if let Some(existing) = combined.get(&item.id) {
                if item.relevance_score.unwrap_or(0.0) > existing.relevance_score.unwrap_or(0.0) {
                    combined.insert(item.id.clone(), item);
                }
            } else {
                combined.insert(item.id.clone(), item);
            }
        }
        
        combined.into_values().collect()
    }

    /// Get memory statistics
    pub async fn get_stats(&self) -> Result<serde_json::Value> {
        let total_items = sqlx::query_scalar!(
            "SELECT COUNT(*) FROM memory_items"
        )
        .fetch_one(&self.db)
        .await?;

        let total_accesses = sqlx::query_scalar!(
            "SELECT SUM(access_count) FROM memory_items"
        )
        .fetch_one(&self.db)
        .await?;

        Ok(serde_json::json!({
            "total_items": total_items,
            "total_accesses": total_accesses.unwrap_or(0),
            "vector_store_size": self.vector_store.size().await?,
        }))
    }
}
