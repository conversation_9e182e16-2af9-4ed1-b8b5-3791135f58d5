import { create } from 'zustand';
import { invoke } from '@tauri-apps/api/tauri';

export interface Tab {
  id: string;
  title: string;
  url: string;
  favicon?: string;
  is_active: boolean;
  is_loading: boolean;
  created_at: string;
  last_accessed: string;
  visit_count: number;
}

export interface MemoryItem {
  id: string;
  content: string;
  metadata: any;
  created_at: string;
  updated_at: string;
  access_count: number;
  last_accessed: string;
  tags: string[];
  relevance_score?: number;
}

interface BrowserState {
  // Tabs
  tabs: Tab[];
  activeTabId: string | null;
  isLoading: boolean;
  
  // Memory
  memoryItems: MemoryItem[];
  searchQuery: string;
  
  // Actions
  createTab: (url: string) => Promise<void>;
  closeTab: (tabId: string) => Promise<void>;
  switchTab: (tabId: string) => Promise<void>;
  updateTabs: () => Promise<void>;
  
  // Memory actions
  searchMemory: (query: string) => Promise<void>;
  saveMemory: (content: string, metadata: any) => Promise<void>;
  setSearchQuery: (query: string) => void;
  
  // AI actions
  getAISuggestion: (context: string) => Promise<string>;
}

export const useBrowserStore = create<BrowserState>((set, get) => ({
  // Initial state
  tabs: [],
  activeTabId: null,
  isLoading: false,
  memoryItems: [],
  searchQuery: '',

  // Tab actions
  createTab: async (url: string) => {
    try {
      set({ isLoading: true });
      const tab = await invoke<Tab>('create_tab', { url });
      await get().updateTabs();
    } catch (error) {
      console.error('Failed to create tab:', error);
    } finally {
      set({ isLoading: false });
    }
  },

  closeTab: async (tabId: string) => {
    try {
      await invoke('close_tab', { tabId });
      await get().updateTabs();
    } catch (error) {
      console.error('Failed to close tab:', error);
    }
  },

  switchTab: async (tabId: string) => {
    try {
      await invoke('switch_tab', { tabId });
      set({ activeTabId: tabId });
      await get().updateTabs();
    } catch (error) {
      console.error('Failed to switch tab:', error);
    }
  },

  updateTabs: async () => {
    try {
      const tabs = await invoke<Tab[]>('get_tabs');
      const activeTab = tabs.find(tab => tab.is_active);
      set({ 
        tabs, 
        activeTabId: activeTab?.id || null 
      });
    } catch (error) {
      console.error('Failed to update tabs:', error);
    }
  },

  // Memory actions
  searchMemory: async (query: string) => {
    try {
      if (!query.trim()) {
        set({ memoryItems: [] });
        return;
      }
      
      const items = await invoke<MemoryItem[]>('search_memory', { 
        query, 
        limit: 20 
      });
      set({ memoryItems: items });
    } catch (error) {
      console.error('Failed to search memory:', error);
      set({ memoryItems: [] });
    }
  },

  saveMemory: async (content: string, metadata: any) => {
    try {
      await invoke('save_memory', { content, metadata });
      // Refresh memory search if there's an active query
      const { searchQuery } = get();
      if (searchQuery) {
        await get().searchMemory(searchQuery);
      }
    } catch (error) {
      console.error('Failed to save memory:', error);
    }
  },

  setSearchQuery: (query: string) => {
    set({ searchQuery: query });
  },

  // AI actions
  getAISuggestion: async (context: string) => {
    try {
      const suggestion = await invoke<string>('get_ai_suggestion', { context });
      return suggestion;
    } catch (error) {
      console.error('Failed to get AI suggestion:', error);
      return 'AI suggestion temporarily unavailable.';
    }
  },
}));
