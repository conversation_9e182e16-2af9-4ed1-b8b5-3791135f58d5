{"rustc": 1842507548689473721, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 2225463790103693989, "path": 9154049589595687132, "deps": [[530211389790465181, "hex", false, 9508896561565191631], [996810380461694889, "sqlx_core", false, 788978114756757290], [1441306149310335789, "tempfile", false, 2701532668765600501], [2713742371683562785, "syn", false, 14621964450831803193], [3060637413840920116, "proc_macro2", false, 14143828410907889523], [3150220818285335163, "url", false, 5650291493764149010], [3405707034081185165, "dotenvy", false, 8542197826090300831], [3722963349756955755, "once_cell", false, 1128676604584301], [8045585743974080694, "heck", false, 7786237867213721939], [9689903380558560274, "serde", false, 3158597207148056356], [9857275760291862238, "sha2", false, 7000312947546632887], [11838249260056359578, "sqlx_sqlite", false, 8420999980532493832], [12170264697963848012, "either", false, 1332962060075666494], [12393800526703971956, "tokio", false, 3833170481708910760], [15367738274754116744, "serde_json", false, 16756031453236183760], [17990358020177143287, "quote", false, 9791753232960825671]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\sqlx-macros-core-463a832e37874991\\dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}